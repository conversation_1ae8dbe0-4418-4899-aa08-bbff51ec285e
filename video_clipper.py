import subprocess
import os
import sys
import logging
from typing import List, Dict, Optional
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VideoClipper:
    """
    A class to handle video clipping and concatenation using FFmpeg.
    """
    
    def __init__(self, temp_dir: str = "temp_clips"):
        """
        Initialize the VideoClipper.
        
        Args:
            temp_dir (str): Directory to store temporary clip files
        """
        self.temp_dir = temp_dir
        self.ensure_temp_directory()
        self.check_ffmpeg_availability()
    
    def ensure_temp_directory(self):
        """Ensure the temporary directory exists."""
        os.makedirs(self.temp_dir, exist_ok=True)
        logger.info(f"Temporary directory ensured: {self.temp_dir}")
    
    def check_ffmpeg_availability(self) -> bool:
        """
        Check if FFmpeg is available on the system.
        
        Returns:
            bool: True if FFmpeg is available, False otherwise
        """
        try:
            subprocess.run(["ffmpeg", "-version"], check=True, capture_output=True)
            logger.info("FFmpeg found and is available.")
            return True
        except (FileNotFoundError, subprocess.CalledProcessError):
            logger.error("FFmpeg command not found or failed. Please ensure FFmpeg is installed.")
            return False
    
    def seconds_to_timestamp(self, seconds: float) -> str:
        """
        Convert seconds to HH:MM:SS.mmm format.
        
        Args:
            seconds (float): Time in seconds
            
        Returns:
            str: Formatted timestamp
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
    
    def get_video_info(self, input_file: str) -> dict:
        """
        Get video information using FFprobe.

        Args:
            input_file (str): Path to the input video file

        Returns:
            dict: Video information including duration, format, etc.
        """
        cmd = [
            "ffprobe",
            "-v", "quiet",
            "-print_format", "json",
            "-show_format",
            "-show_streams",
            input_file
        ]

        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            import json
            info = json.loads(result.stdout)

            # Extract video stream info
            video_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'video']
            audio_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'audio']

            duration = float(info.get('format', {}).get('duration', 0))

            logger.info(f"Video info for {input_file}:")
            logger.info(f"  Duration: {duration} seconds")
            logger.info(f"  Video streams: {len(video_streams)}")
            logger.info(f"  Audio streams: {len(audio_streams)}")
            if video_streams:
                logger.info(f"  Video codec: {video_streams[0].get('codec_name')}")
                logger.info(f"  Video resolution: {video_streams[0].get('width')}x{video_streams[0].get('height')}")

            return {
                'duration': duration,
                'video_streams': video_streams,
                'audio_streams': audio_streams,
                'format': info.get('format', {})
            }
        except Exception as e:
            logger.error(f"Failed to get video info: {e}")
            return {}

    def clip_video_segment(self, input_file: str, start_time: float, end_time: float,
                          output_file: str, re_encode: bool = True) -> bool:
        """
        Clip a segment from an input video using FFmpeg.

        Args:
            input_file (str): Path to the input video file
            start_time (float): Start time in seconds
            end_time (float): End time in seconds
            output_file (str): Path for the output clipped video file
            re_encode (bool): Whether to re-encode the video for accuracy

        Returns:
            bool: True if clipping was successful, False otherwise
        """
        # Debug logging
        logger.info(f"=== CLIPPING DEBUG INFO ===")
        logger.info(f"Input file: {input_file}")
        logger.info(f"Input file exists: {os.path.exists(input_file)}")
        logger.info(f"Input file size: {os.path.getsize(input_file) if os.path.exists(input_file) else 'N/A'} bytes")
        logger.info(f"Start time: {start_time} seconds")
        logger.info(f"End time: {end_time} seconds")
        logger.info(f"Duration: {end_time - start_time} seconds")
        logger.info(f"Output file: {output_file}")

        # Get video information
        video_info = self.get_video_info(input_file)
        video_duration = video_info.get('duration', 0)

        if video_duration > 0:
            logger.info(f"Video total duration: {video_duration} seconds")
            if start_time >= video_duration:
                logger.error(f"Start time {start_time} is beyond video duration {video_duration}")
                return False
            if end_time > video_duration:
                logger.warning(f"End time {end_time} is beyond video duration {video_duration}, adjusting to {video_duration}")
                end_time = video_duration

        # Ensure the output directory exists
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info(f"Created directory: {output_dir}")

        # Convert seconds to timestamp format
        start_ts = self.seconds_to_timestamp(start_time)
        end_ts = self.seconds_to_timestamp(end_time)
        duration_ts = self.seconds_to_timestamp(end_time - start_time)

        logger.info(f"Timestamp conversion:")
        logger.info(f"  Start: {start_time}s -> {start_ts}")
        logger.info(f"  End: {end_time}s -> {end_ts}")
        logger.info(f"  Duration: {end_time - start_time}s -> {duration_ts}")

        # Build FFmpeg command with more explicit parameters
        cmd = [
            "ffmpeg",
            "-i", input_file,
            "-ss", str(start_time),  # Use seconds directly for more accuracy
            "-t", str(end_time - start_time),  # Use duration instead of end time
            "-avoid_negative_ts", "make_zero",
            "-y",  # Overwrite output file without asking
        ]

        # Choose encoding options - default to re-encode for accuracy
        if re_encode:
            cmd.extend([
                "-c:v", "libx264",
                "-c:a", "aac",
                "-preset", "fast",
                "-crf", "23",  # Good quality
                "-movflags", "+faststart"  # Optimize for web playback
            ])
        else:
            cmd.extend(["-c", "copy"])  # Stream copy for speed

        cmd.append(output_file)

        logger.info(f"FFmpeg command: {' '.join(cmd)}")

        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)

            # Check if output file was created and has reasonable size
            if os.path.exists(output_file):
                output_size = os.path.getsize(output_file)
                logger.info(f"Output file created: {output_file}")
                logger.info(f"Output file size: {output_size} bytes")

                # Get info about the created clip
                clip_info = self.get_video_info(output_file)
                clip_duration = clip_info.get('duration', 0)
                logger.info(f"Clip duration: {clip_duration} seconds")

                if output_size < 1000:  # Less than 1KB is suspicious
                    logger.warning(f"Output file is very small ({output_size} bytes), might be empty")

                if abs(clip_duration - (end_time - start_time)) > 1.0:  # More than 1 second difference
                    logger.warning(f"Clip duration {clip_duration}s differs significantly from expected {end_time - start_time}s")

                return True
            else:
                logger.error(f"Output file was not created: {output_file}")
                return False

        except FileNotFoundError:
            logger.error("FFmpeg command not found. Is FFmpeg installed and in your PATH?")
            return False
        except subprocess.CalledProcessError as e:
            logger.error(f"Error during FFmpeg clipping process for {output_file}:")
            logger.error(f"Command: {' '.join(e.cmd)}")
            logger.error(f"Return Code: {e.returncode}")
            logger.error(f"FFmpeg stdout: {e.stdout}")
            logger.error(f"FFmpeg stderr: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"An unexpected error occurred during clipping: {e}")
            return False
    
    def create_clips_from_timestamps(self, input_video: str, timestamps: List[Dict], 
                                   cache_key: str) -> List[str]:
        """
        Create video clips from a list of timestamp dictionaries.
        
        Args:
            input_video (str): Path to the input video file
            timestamps (List[Dict]): List of timestamp dictionaries with 'timestampstart' and 'timestampend'
            cache_key (str): Unique identifier for this video
            
        Returns:
            List[str]: List of paths to successfully created clip files
        """
        if not os.path.exists(input_video):
            logger.error(f"Input video file '{input_video}' not found.")
            return []
        
        successful_clips = []
        clip_dir = os.path.join(self.temp_dir, cache_key)
        os.makedirs(clip_dir, exist_ok=True)
        
        for i, ts in enumerate(timestamps):
            start_time = ts.get('timestampstart')
            end_time = ts.get('timestampend')
            
            if start_time is None or end_time is None:
                logger.warning(f"Skipping clip {i}: missing timestamp data")
                continue
            
            if start_time >= end_time:
                logger.warning(f"Skipping clip {i}: invalid timestamp range ({start_time} >= {end_time})")
                continue
            
            clip_output = os.path.join(clip_dir, f'clip_{i}.mp4')
            logger.info(f"Processing clip {i+1}/{len(timestamps)}: {start_time}s to {end_time}s")
            
            if self.clip_video_segment(input_video, start_time, end_time, clip_output):
                successful_clips.append(clip_output)
            else:
                logger.warning(f"Failed to create clip: {clip_output}")
        
        return successful_clips
    
    def concatenate_clips(self, clip_files: List[str], output_file: str) -> bool:
        """
        Concatenate multiple video clips into a single video.
        
        Args:
            clip_files (List[str]): List of paths to clip files
            output_file (str): Path for the output concatenated video
            
        Returns:
            bool: True if concatenation was successful, False otherwise
        """
        if not clip_files:
            logger.error("No clips provided for concatenation")
            return False
        
        # Create concatenation list file
        concat_file = os.path.join(self.temp_dir, 'concat_list.txt')
        try:
            with open(concat_file, 'w') as f:
                for clip in clip_files:
                    abs_clip_path = os.path.abspath(clip)
                    f.write(f"file '{abs_clip_path}'\n")
            logger.info(f"Created concatenation list: {concat_file}")
        except IOError as e:
            logger.error(f"Error writing concatenation list file {concat_file}: {e}")
            return False
        
        # Build FFmpeg concatenation command
        cmd_concat = [
            "ffmpeg",
            "-f", "concat",
            "-safe", "0",
            "-i", concat_file,
            "-c", "copy",
            "-y",
            output_file
        ]
        
        logger.info(f"Concatenating {len(clip_files)} clips into: {output_file}")
        logger.debug(f"FFmpeg command: {' '.join(cmd_concat)}")
        
        try:
            result = subprocess.run(cmd_concat, check=True, capture_output=True, text=True)
            logger.info(f"Successfully created concatenated video: {output_file}")
            
            # Clean up concatenation list file
            try:
                os.remove(concat_file)
            except OSError:
                pass
            
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error during FFmpeg concatenation process:")
            logger.error(f"Command: {' '.join(e.cmd)}")
            logger.error(f"Return Code: {e.returncode}")
            logger.error(f"FFmpeg stderr: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"An unexpected error occurred during concatenation: {e}")
            return False
    
    def process_video_with_timestamps(self, input_video: str, timestamps: List[Dict], 
                                    cache_key: str, output_filename: str = None) -> Optional[str]:
        """
        Process a video by creating clips from timestamps and concatenating them.
        
        Args:
            input_video (str): Path to the input video file
            timestamps (List[Dict]): List of timestamp dictionaries
            cache_key (str): Unique identifier for this video
            output_filename (str): Optional custom output filename
            
        Returns:
            Optional[str]: Path to the final concatenated video, or None if failed
        """
        if not timestamps:
            logger.warning("No timestamps provided for video processing")
            return None
        
        # Create clips
        clip_files = self.create_clips_from_timestamps(input_video, timestamps, cache_key)
        
        if not clip_files:
            logger.error("No clips were successfully created")
            return None
        
        # Generate output filename
        if not output_filename:
            output_filename = f"{cache_key}_concatenated.mp4"
        
        output_path = os.path.join(self.temp_dir, output_filename)
        
        # Concatenate clips
        if self.concatenate_clips(clip_files, output_path):
            return output_path
        else:
            return None
    
    def cleanup_temp_files(self, cache_key: str = None):
        """
        Clean up temporary files.
        
        Args:
            cache_key (str): If provided, only clean files for this specific cache key
        """
        try:
            if cache_key:
                clip_dir = os.path.join(self.temp_dir, cache_key)
                if os.path.exists(clip_dir):
                    import shutil
                    shutil.rmtree(clip_dir)
                    logger.info(f"Cleaned up temporary files for {cache_key}")
            else:
                # Clean up all temp files
                for item in os.listdir(self.temp_dir):
                    item_path = os.path.join(self.temp_dir, item)
                    if os.path.isfile(item_path):
                        os.remove(item_path)
                    elif os.path.isdir(item_path):
                        import shutil
                        shutil.rmtree(item_path)
                logger.info("Cleaned up all temporary files")
        except OSError as e:
            logger.error(f"Error during cleanup: {e}")
