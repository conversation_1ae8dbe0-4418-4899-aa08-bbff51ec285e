from fastapi import <PERSON><PERSON><PERSON>, HTTPException, File, UploadFile, Form, Depends, Body
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pathlib import Path
from whisperX import process_video_for_transcription
from llm_gemini import find_answer_with_timestamp
from fastapi.middleware.cors import CORSMiddleware
from video_clipper import VideoClipper
import yt_dlp as youtube_dl
import logging
from pydantic import BaseModel
import os
import uuid
import hashlib
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins (for testing)
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

# Initialize video clipper
video_clipper = VideoClipper()

# Mount static files for serving clipped videos
app.mount("/clips", StaticFiles(directory="temp_clips"), name="clips")

# Cache configuration
CACHE_DIR = "video_cache"
CACHE_INDEX_FILE = "cache_index.json"

class YouTubeRequest(BaseModel):
    youtube_url: str
    query: str
    output_dir: str = "."
    file_path: str = "timestamps.json"
    audio_only: bool = True  # New option to control audio-only download

class VideoClipRequest(BaseModel):
    youtube_url: str
    query: str
    generate_clips: bool = True

def ensure_cache_directory():
    """Ensure cache directory and index file exist."""
    os.makedirs(CACHE_DIR, exist_ok=True)
    if not os.path.exists(CACHE_INDEX_FILE):
        with open(CACHE_INDEX_FILE, 'w') as f:
            json.dump({}, f)

def get_video_id_from_url(url: str) -> str:
    """Extract video ID from YouTube URL using yt-dlp."""
    try:
        with youtube_dl.YoutubeDL({'quiet': True}) as ydl:
            info = ydl.extract_info(url, download=False)
            return info.get('id', '')
    except Exception as e:
        logging.warning(f"Could not extract video ID from URL: {e}")
        # Fallback: create hash from URL
        return hashlib.md5(url.encode()).hexdigest()

def get_cache_key(video_url: str) -> str:
    """Generate cache key from video URL."""
    video_id = get_video_id_from_url(video_url)
    return f"youtube_{video_id}"

def load_cache_index() -> dict:
    """Load the cache index from file."""
    try:
        with open(CACHE_INDEX_FILE, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_cache_index(cache_index: dict):
    """Save the cache index to file."""
    with open(CACHE_INDEX_FILE, 'w') as f:
        json.dump(cache_index, f, indent=2)

def get_cached_video_info(cache_key: str) -> dict:
    """Get cached video information if it exists."""
    cache_index = load_cache_index()
    cached_entry = cache_index.get(cache_key)
    
    if not cached_entry:
        return None
    
    cache_dir = cached_entry.get('cache_dir')
    if not cache_dir or not os.path.exists(cache_dir):
        # Cache directory doesn't exist, remove from index
        cache_index.pop(cache_key, None)
        save_cache_index(cache_index)
        return None
    
    # Verify that required files exist
    required_files = [
        os.path.join(cache_dir, "timestamps.json"),
        os.path.join(cache_dir, "timestamps.txt"),
        os.path.join(cache_dir, "transcription.txt")
    ]
    
    if not all(os.path.exists(f) for f in required_files):
        logging.warning(f"Cache incomplete for {cache_key}, will reprocess")
        return None
    
    return cached_entry

def cache_video_info(cache_key: str, cache_dir: str, video_info: dict):
    """Cache video information."""
    cache_index = load_cache_index()
    cache_index[cache_key] = {
        'cache_dir': cache_dir,
        'video_info': video_info,
        'timestamp': os.path.getctime(cache_dir) if os.path.exists(cache_dir) else None
    }
    save_cache_index(cache_index)

def transcription_files_exist(output_dir: str, file_path: str) -> bool:
    """
    Check if transcription files already exist in the output directory.
    """
    timestamps_path = os.path.join(output_dir, file_path)
    txt_path = os.path.join(output_dir, "timestamps.txt")
    transcription_path = os.path.join(output_dir, "transcription.txt")
    
    return all(os.path.exists(path) for path in [timestamps_path, txt_path, transcription_path])

@app.post("/process_youtube_video/")
async def process_youtube_video(request: YouTubeRequest):
    """
    API endpoint that processes a YouTube video, transcribes it, and returns the best answer to the query.
    Uses caching to avoid redownloading and reprocessing the same video.
    """
    import traceback
    logging.info(f"YouTube request received for URL: {request.youtube_url}")

    try:
        # Ensure cache directory exists
        ensure_cache_directory()
        
        # Generate cache key
        cache_key = get_cache_key(request.youtube_url)
        logging.info(f"Cache key for video: {cache_key}")
        
        # Check if video is already cached
        cached_info = get_cached_video_info(cache_key)
        
        if cached_info:
            logging.info(f"Video found in cache: {cache_key}")
            cache_dir = cached_info['cache_dir']
            video_info = cached_info['video_info']
            
            # Use cached transcription files
            result = find_answer_with_timestamp(request.query, os.path.join(cache_dir, request.file_path))
            if not result or (isinstance(result, dict) and "error" in result):
                logging.error(f"No valid answer found for query: {request.query}. Error: {result.get('error') if isinstance(result, dict) else result}")
                raise HTTPException(status_code=404, detail="No valid answer found.")
            
            response = {
                "answer": result,
                "video_info": video_info,
                "cached": True
            }
            return response
        
        # Video not in cache, proceed with download and processing
        logging.info(f"Video not in cache, downloading: {request.youtube_url}")
        
        # Create cache directory for this video
        cache_dir = os.path.join(CACHE_DIR, cache_key)
        os.makedirs(cache_dir, exist_ok=True)

        # Download YouTube video/audio using yt-dlp
        logging.info(f"Downloading YouTube video using yt-dlp: {request.youtube_url}")
        ydl_opts = {
            'outtmpl': os.path.join(cache_dir, f"{cache_key}.%(ext)s"),
            'noplaylist': True,
            'quiet': True,
        }
        # Prefer audio only if requested, fallback to best video+audio
        tried_audio = False
        video_info = None
        download_success = False
        if getattr(request, 'audio_only', True):
            ydl_opts['format'] = 'bestaudio/best'
            tried_audio = True
            try:
                with youtube_dl.YoutubeDL(ydl_opts) as ydl:
                    info_dict = ydl.extract_info(request.youtube_url, download=True)
                    video_info = {
                        "title": info_dict.get('title', 'N/A'),
                        "author": info_dict.get('uploader', 'N/A'),
                        "length_seconds": info_dict.get('duration', 'N/A'),
                        "thumbnail_url": info_dict.get('thumbnail', 'N/A')
                    }
                download_success = True
            except youtube_dl.DownloadError as e:
                logging.warning(f"Audio-only download failed, will try best video+audio. Error: {e}")
        if not download_success:
            ydl_opts['format'] = 'bestvideo+bestaudio/best'
            try:
                with youtube_dl.YoutubeDL(ydl_opts) as ydl:
                    info_dict = ydl.extract_info(request.youtube_url, download=True)
                    video_info = {
                        "title": info_dict.get('title', 'N/A'),
                        "author": info_dict.get('uploader', 'N/A'),
                        "length_seconds": info_dict.get('duration', 'N/A'),
                        "thumbnail_url": info_dict.get('thumbnail', 'N/A')
                    }
                download_success = True
            except youtube_dl.DownloadError as e:
                # Try to log available formats
                try:
                    with youtube_dl.YoutubeDL({'quiet': False}) as ydl:
                        formats_info = ydl.extract_info(request.youtube_url, download=False)
                        available_formats = [f["format"] for f in formats_info.get("formats", [])]
                        logging.error(f"Available formats for {request.youtube_url}: {available_formats}")
                except Exception as e2:
                    logging.error(f"Could not list formats: {e2}")
                raise HTTPException(status_code=400, detail=f"Video/audio download error: {str(e)}")
        # Find the actual downloaded file path
        downloaded_files = [f for f in os.listdir(cache_dir) if f.startswith(cache_key)]
        if not downloaded_files:
            logging.error("yt-dlp failed to download the video/audio.")
            raise Exception("yt-dlp failed to download the video/audio.")
        video_filename = downloaded_files[0]
        video_path = os.path.join(cache_dir, video_filename)
        logging.info(f"Downloaded file to: {video_path}")

        # Process the video/audio for transcription (always needed for new cache entries)
        logging.info("Processing video/audio for transcription...")
        process_video_for_transcription(video_path, cache_dir)
        
        # Cache the video information
        cache_video_info(cache_key, cache_dir, video_info)
        logging.info(f"Video/audio cached successfully: {cache_key}")

        # Find the best answer based on the query
        result = find_answer_with_timestamp(request.query, os.path.join(cache_dir, request.file_path))
        print('result=>',result)
        if not result or (isinstance(result, dict) and "error" in result):
            logging.error(f"No valid answer found for query: {request.query}. Error: {result.get('error') if isinstance(result, dict) else result}")
            raise HTTPException(status_code=404, detail="No valid answer found.")

        # Add video/audio metadata to the response
        response = {
            "answer": result,
            "video_info": video_info,
            "cached": False
        }

        return response

    except youtube_dl.DownloadError as e:
        logging.error(f"yt-dlp download error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=400, detail=f"Video download error: {str(e)}")
    except HTTPException as e:
        logging.error(f"HTTPException: {e.detail}\n{traceback.format_exc()}")
        raise
    except Exception as e:
        logging.error(f"Error processing YouTube video: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/process_video_and_find_answer/")
async def process_video_and_find_answer(
    video_file: UploadFile = File(...),
    query: str = Form(...),
    output_dir: str = Form(default="."),
    file_path: str = Form(default="timestamps.json"),
):
    """
    API endpoint that processes a video, transcribes it, and returns the best answer to the query.
    """
    logging.info("File upload request received.")

    try:
        # Create a unique directory for this request
        request_id = str(uuid.uuid4())
        request_output_dir = os.path.join(output_dir, request_id)
        os.makedirs(request_output_dir, exist_ok=True)

        # Save uploaded file safely
        video_path = Path(f"{request_output_dir}/{video_file.filename}")
        with video_path.open("wb") as buffer:
            buffer.write(await video_file.read())

        logging.info(f"Video saved to: {video_path}")

        # Check if transcription files already exist
        if not transcription_files_exist(request_output_dir, file_path):
            logging.info("Transcription files not found, processing video...")
            # Step 1: Process the video for transcription
            process_video_for_transcription(str(video_path), request_output_dir)
        else:
            logging.info("Using existing transcription files...")

        # Step 2: Find the best answer based on the query
        result_file_path = os.path.join(request_output_dir, file_path)
        result = find_answer_with_timestamp(query, result_file_path)

        if not result or (isinstance(result, dict) and "error" in result):
            raise HTTPException(status_code=404, detail="No valid answer found.")

        return {"answer": result}

    except Exception as e:
        logging.error(f"Error processing uploaded video: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate_video_clips/")
async def generate_video_clips(request: VideoClipRequest):
    """
    API endpoint that processes a YouTube video, finds timestamps, and generates clipped video segments.
    """
    import traceback
    logging.info(f"Video clip request received for URL: {request.youtube_url}")

    try:
        # Ensure cache directory exists
        ensure_cache_directory()

        # Generate cache key
        cache_key = get_cache_key(request.youtube_url)
        logging.info(f"Cache key for video: {cache_key}")

        # Check if video is already cached
        cached_info = get_cached_video_info(cache_key)

        # Check if we need to download video format (not just audio)
        need_video_download = False
        if not cached_info:
            need_video_download = True
            logging.info(f"Video not in cache, downloading with video format: {request.youtube_url}")
        else:
            # Check if cached file has video streams
            cache_dir = cached_info['cache_dir']
            downloaded_files = [f for f in os.listdir(cache_dir) if f.startswith(cache_key) and not f.endswith('.json') and not f.endswith('.txt')]
            if downloaded_files:
                video_path = os.path.join(cache_dir, downloaded_files[0])
                # Check if the cached file has video streams
                try:
                    import subprocess
                    result = subprocess.run([
                        "ffprobe", "-v", "quiet", "-select_streams", "v",
                        "-show_entries", "stream=codec_type", "-of", "csv=p=0", video_path
                    ], capture_output=True, text=True)

                    if not result.stdout.strip():  # No video streams found
                        logging.info(f"Cached file is audio-only, need to re-download with video: {video_path}")
                        need_video_download = True
                    else:
                        logging.info(f"Cached file has video streams: {video_path}")
                except Exception as e:
                    logging.warning(f"Could not check video streams in cached file: {e}")
                    need_video_download = True
            else:
                need_video_download = True

        if need_video_download:
            # Create cache directory for this video
            cache_dir = os.path.join(CACHE_DIR, cache_key)
            os.makedirs(cache_dir, exist_ok=True)

            # Download YouTube video using yt-dlp (force video format for clipping)
            logging.info(f"Downloading YouTube video using yt-dlp: {request.youtube_url}")
            ydl_opts = {
                'outtmpl': os.path.join(cache_dir, f"{cache_key}_video.%(ext)s"),  # Different filename for video
                'noplaylist': True,
                'quiet': True,
                'format': 'bestvideo+bestaudio/best'  # Force video download for clipping
            }

            video_info = None
            try:
                with youtube_dl.YoutubeDL(ydl_opts) as ydl:
                    info_dict = ydl.extract_info(request.youtube_url, download=True)
                    video_info = {
                        "title": info_dict.get('title', 'N/A'),
                        "author": info_dict.get('uploader', 'N/A'),
                        "length_seconds": info_dict.get('duration', 'N/A'),
                        "thumbnail_url": info_dict.get('thumbnail', 'N/A')
                    }
            except youtube_dl.DownloadError as e:
                raise HTTPException(status_code=400, detail=f"Video download error: {str(e)}")

            # Find the actual downloaded file path
            downloaded_files = [f for f in os.listdir(cache_dir) if f.startswith(f"{cache_key}_video")]
            if not downloaded_files:
                logging.error("yt-dlp failed to download the video.")
                raise Exception("yt-dlp failed to download the video.")

            video_filename = downloaded_files[0]
            video_path = os.path.join(cache_dir, video_filename)
            logging.info(f"Downloaded video file to: {video_path}")

            # Process the video for transcription if not already done
            if not transcription_files_exist(cache_dir, "timestamps.json"):
                logging.info("Processing video for transcription...")
                process_video_for_transcription(video_path, cache_dir)

            # Update cache with video info (don't overwrite existing cache)
            if not cached_info:
                cache_video_info(cache_key, cache_dir, video_info)
            logging.info(f"Video with video streams cached successfully: {cache_key}")
        else:
            cache_dir = cached_info['cache_dir']
            video_info = cached_info['video_info']

            # Find the video file in cache (prefer video file over audio file)
            video_files = [f for f in os.listdir(cache_dir) if f.startswith(f"{cache_key}_video")]
            if video_files:
                video_filename = video_files[0]
                video_path = os.path.join(cache_dir, video_filename)
                logging.info(f"Using cached video file: {video_path}")
            else:
                # Fallback to any file with cache_key
                downloaded_files = [f for f in os.listdir(cache_dir) if f.startswith(cache_key) and not f.endswith('.json') and not f.endswith('.txt')]
                if not downloaded_files:
                    logging.error("Cached video file not found, need to re-download")
                    raise HTTPException(status_code=404, detail="Cached video file not found")

                video_filename = downloaded_files[0]
                video_path = os.path.join(cache_dir, video_filename)
                logging.info(f"Using cached file: {video_path}")

        # Find timestamps based on the query
        result = find_answer_with_timestamp(request.query, os.path.join(cache_dir, "timestamps.json"))
        if not result or (isinstance(result, dict) and "error" in result):
            logging.error(f"No valid answer found for query: {request.query}")
            raise HTTPException(status_code=404, detail="No valid answer found.")

        # Generate video clips if requested
        clipped_video_url = None
        if request.generate_clips and isinstance(result, list):
            # Filter results that have valid timestamps
            valid_timestamps = [r for r in result if r.get('timestampstart') is not None and r.get('timestampend') is not None]

            logging.info(f"=== VIDEO CLIPPING DEBUG ===")
            logging.info(f"Total results: {len(result)}")
            logging.info(f"Valid timestamps found: {len(valid_timestamps)}")

            for i, ts in enumerate(valid_timestamps):
                logging.info(f"Timestamp {i+1}: start={ts.get('timestampstart')}, end={ts.get('timestampend')}, duration={ts.get('timestampend', 0) - ts.get('timestampstart', 0)}")

            logging.info(f"Video file path: {video_path}")
            logging.info(f"Video file exists: {os.path.exists(video_path)}")
            if os.path.exists(video_path):
                logging.info(f"Video file size: {os.path.getsize(video_path)} bytes")

            if valid_timestamps:
                logging.info(f"Generating clips for {len(valid_timestamps)} timestamp segments")

                # Generate clipped video
                output_filename = f"{cache_key}_clips.mp4"
                clipped_video_path = video_clipper.process_video_with_timestamps(
                    video_path, valid_timestamps, cache_key, output_filename
                )

                if clipped_video_path:
                    # Return URL path for the clipped video
                    clipped_video_url = f"/clips/{output_filename}"
                    logging.info(f"Clipped video generated: {clipped_video_url}")
                    logging.info(f"Clipped video file exists: {os.path.exists(clipped_video_path)}")
                    if os.path.exists(clipped_video_path):
                        logging.info(f"Clipped video file size: {os.path.getsize(clipped_video_path)} bytes")
                else:
                    logging.warning("Failed to generate clipped video")
            else:
                logging.warning("No valid timestamps found for clipping")

        response = {
            "answer": result,
            "video_info": video_info,
            "clipped_video_url": clipped_video_url,
            "cached": cached_info is not None
        }

        return response

    except youtube_dl.DownloadError as e:
        logging.error(f"yt-dlp download error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=400, detail=f"Video download error: {str(e)}")
    except HTTPException as e:
        logging.error(f"HTTPException: {e.detail}\n{traceback.format_exc()}")
        raise
    except Exception as e:
        logging.error(f"Error processing video clips: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/cache/status")
async def get_cache_status():
    """Get information about the current cache status."""
    ensure_cache_directory()
    cache_index = load_cache_index()
    
    cache_stats = {
        "total_cached_videos": len(cache_index),
        "cache_directory": CACHE_DIR,
        "cached_videos": []
    }
    
    for cache_key, cache_info in cache_index.items():
        video_info = cache_info.get('video_info', {})
        cache_stats["cached_videos"].append({
            "cache_key": cache_key,
            "title": video_info.get("title", "N/A"),
            "author": video_info.get("author", "N/A"),
            "cache_dir": cache_info.get('cache_dir'),
            "exists": os.path.exists(cache_info.get('cache_dir', ''))
        })
    
    return cache_stats

@app.delete("/cache/clear")
async def clear_cache():
    """Clear all cached videos."""
    try:
        ensure_cache_directory()
        cache_index = load_cache_index()
        
        # Remove cache directories
        removed_count = 0
        for cache_key, cache_info in cache_index.items():
            cache_dir = cache_info.get('cache_dir')
            if cache_dir and os.path.exists(cache_dir):
                import shutil
                shutil.rmtree(cache_dir)
                removed_count += 1
        
        # Clear the cache index
        save_cache_index({})
        
        return {
            "message": f"Cache cleared successfully. Removed {removed_count} cached videos.",
            "removed_count": removed_count
        }
    except Exception as e:
        logging.error(f"Error clearing cache: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing cache: {str(e)}")

@app.delete("/cache/{cache_key}")
async def remove_cached_video(cache_key: str):
    """Remove a specific video from cache."""
    try:
        ensure_cache_directory()
        cache_index = load_cache_index()
        
        if cache_key not in cache_index:
            raise HTTPException(status_code=404, detail="Video not found in cache")
        
        # Remove the cache directory
        cache_info = cache_index[cache_key]
        cache_dir = cache_info.get('cache_dir')
        if cache_dir and os.path.exists(cache_dir):
            import shutil
            shutil.rmtree(cache_dir)
        
        # Remove from index
        del cache_index[cache_key]
        save_cache_index(cache_index)
        
        return {"message": f"Cached video {cache_key} removed successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error removing cached video: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error removing cached video: {str(e)}")