from langchain_google_genai import ChatG<PERSON>gleGenerativeA<PERSON>, GoogleGenerativeAIEmbeddings
from langchain.chains import RetrievalQA
from langchain.vectorstores import FAISS
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.prompts import PromptTemplate
import json
import os

# Global variable to store the cached transcription data
_TRANSCRIPTION_CACHE = {
    'file_path': None,
    'data': None,
    'mtime': None
}

def load_transcription_data(file_path):
    """
    Load transcription data with file modification time check.
    Caches the data to avoid repeated file reads.
    Args:
        file_path (str): Path to the JSON transcription file
    Returns:
        list: Transcription data
    """
    current_mtime = os.path.getmtime(file_path)
    if (file_path == _TRANSCRIPTION_CACHE['file_path'] and 
        current_mtime == _TRANSCRIPTION_CACHE['mtime']):
        return _TRANSCRIPTION_CACHE['data']
    with open(file_path, "r") as json_file:
        transcription_data = json.load(json_file)
    _TRANSCRIPTION_CACHE['file_path'] = file_path
    _TRANSCRIPTION_CACHE['data'] = transcription_data
    _TRANSCRIPTION_CACHE['mtime'] = current_mtime
    return transcription_data

def find_answer_with_timestamp(query, file_path, qa_model="gemini-2.0-flash", embedder_model="models/embedding-001", top_k=3, device=0, google_api_key=None):
    """
    Find the best answer with a timestamp from a transcription dataset using Gemini via LangChain.
    Args:
        query (str): The question to answer.
        file_path (str): Path to the transcription data (JSON format).
        qa_model (str): Gemini model name (default: gemini-pro).
        embedder_model (str): Google embedding model (default: models/embedding-001).
        top_k (int): Number of top matches to consider.
        device (int): Unused, for API compatibility.
        google_api_key (str): Google API key for Gemini.
    Returns:
        dict: Best answer with associated metadata.
    """
    try:
        if google_api_key is None:
            google_api_key = "AIzaSyB05-1Am4aQR-kL4MompJGU2xJbRpZGh2o"
        if not google_api_key:
            return {"error": "Google API key not provided. Set GOOGLE_API_KEY env variable or pass as argument."}

        # Load transcription data using the caching mechanism
        transcription_data = load_transcription_data(file_path)
        # Prepare context as a JSON string (like in notebook)
        context = json.dumps(transcription_data, ensure_ascii=False)
        # Split context into chunks
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=10000, chunk_overlap=1000)
        texts = text_splitter.split_text(context)
        # Embeddings
        embeddings = GoogleGenerativeAIEmbeddings(model=embedder_model, google_api_key=google_api_key)
        # Vector index
        vector_index = FAISS.from_texts(texts, embeddings).as_retriever(search_kwargs={"k": top_k})
        # Enhanced prompt template for better answers and timestamps
        template = """You are an expert video content analyzer. Based on the provided video transcript segments with timestamps, provide both a detailed answer to the question and the relevant timestamps.

Context (JSON format with text segments and timestamps):
{context}

Question: {question}

Instructions:
1. Provide a comprehensive, accurate answer to the question based on the video content
2. Include specific timestamps where the relevant information is discussed
3. If you find multiple relevant segments, mention all of them
4. Be specific and detailed in your answer while staying concise
5. If you don't know the answer, clearly state that you don't know

Format your response as JSON:
{{
    "answer": "Your detailed answer here",
    "timestamps": [
        {{"start": start_time, "end": end_time, "description": "Brief description of what happens in this segment"}}
    ]
}}

Helpful Answer:"""
        QA_CHAIN_PROMPT = PromptTemplate.from_template(template)
        # LLM
        model = ChatGoogleGenerativeAI(model=qa_model, google_api_key=google_api_key, temperature=0.2, convert_system_message_to_human=True)
        # QA chain
        qa_chain = RetrievalQA.from_chain_type(
            model,
            retriever=vector_index,
            return_source_documents=True,
            chain_type_kwargs={"prompt": QA_CHAIN_PROMPT}
        )
        # Run chain
        result = qa_chain.invoke({"query": query})
        import logging
        temp = result["result"]

        # Enhanced parsing to handle the new JSON format with answers and timestamps
        import re
        try:
            # First try to parse as the new JSON format with answer and timestamps
            json_match = re.search(r'\{.*"answer".*"timestamps".*\}', temp, re.DOTALL)
            if json_match:
                try:
                    data = json.loads(json_match.group(0))
                    answer_text = data.get("answer", "")
                    timestamps = data.get("timestamps", [])

                    results = []
                    if timestamps and isinstance(timestamps, list):
                        for ts in timestamps:
                            if isinstance(ts, dict) and "start" in ts and "end" in ts:
                                results.append({
                                    "query": query,
                                    "answer": answer_text,
                                    "context": ts.get("description", ""),
                                    "timestamp": f"{ts['start']} - {ts['end']}",
                                    "score": None,
                                    "timestampstart": ts["start"],
                                    "timestampend": ts["end"]
                                })

                    # If we have results with timestamps, return them
                    if results:
                        return results

                    # If we have an answer but no valid timestamps, return answer only
                    if answer_text:
                        return [{
                            "query": query,
                            "answer": answer_text,
                            "context": answer_text,
                            "timestamp": None,
                            "score": None,
                            "timestampstart": None,
                            "timestampend": None
                        }]
                except json.JSONDecodeError:
                    logging.warning("Failed to parse new JSON format, trying fallback methods")

            # Fallback: Try to extract JSON array from the output (old format)
            json_match = re.search(r'(\[.*\])', temp, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group(1))
                # Handle [start, end] as a list of floats
                if isinstance(data, list) and len(data) == 2 and all(isinstance(x, (float, int)) for x in data):
                    start, end = data
                    return [{
                        "query": query,
                        "answer": f"Found relevant content at timestamp {start} to {end} seconds",
                        "context": f"{start} - {end}",
                        "timestamp": f"{start} - {end}",
                        "score": None,
                        "timestampstart": start,
                        "timestampend": end
                    }]
                # Otherwise, treat as list of dicts
                answers = []
                for item in data:
                    if isinstance(item, dict):
                        answers.append({
                            "query": query,
                            "answer": item.get("text", f"Found relevant content at timestamp"),
                            "context": item.get("text", ""),
                            "timestamp": f"{item.get('start', '')} - {item.get('end', '')}",
                            "score": None,
                            "timestampstart": item.get("start"),
                            "timestampend": item.get("end")
                        })
                if answers:
                    return answers
                else:
                    return {"error": "No valid answers found"}

            # If not JSON, try to parse as a timestamp string (e.g. '389.325-391.186' or '284.576, 293.927')
            timestamp_match = re.match(r'([\d.]+)[,\s-]+([\d.]+)', temp.strip())
            if timestamp_match:
                start = float(timestamp_match.group(1))
                end = float(timestamp_match.group(2))
                return [{
                    "query": query,
                    "answer": f"Found relevant content at timestamp {start} to {end} seconds",
                    "context": temp.strip(),
                    "timestamp": f"{start} - {end}",
                    "score": None,
                    "timestampstart": start,
                    "timestampend": end
                }]

            # Otherwise, treat the entire response as an answer
            return [{
                "query": query,
                "answer": temp.strip() if temp.strip() else "I couldn't find a specific answer to your question in the video content.",
                "context": temp.strip(),
                "timestamp": None,
                "score": None,
                "timestampstart": None,
                "timestampend": None
            }]
        except Exception as e:
            logging.error(f"Failed to parse LLM output: {e}\nRaw output: {result.get('result', '')}")
            return {"error": f"Failed to parse LLM output: {e}", "raw_result": result.get("result", "")}
    except Exception as e:
        return {"error": str(e)}

# Example Usage
if __name__ == "__main__":
    query = "When does the speaker introduce histograms?"
    file_path = "timestamps.json"
    result = find_answer_with_timestamp(query, file_path)
    if "error" in result:
        print(result["error"])
    else:
        print(result)
