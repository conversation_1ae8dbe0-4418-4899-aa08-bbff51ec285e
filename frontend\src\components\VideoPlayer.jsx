import { useState, useEffect, useRef } from "react";
import {
  FaThumbsUp,
  FaThumbsDown,
  FaShare,
  FaDownload,
  FaEllipsisH,
  FaRegBell,
  FaSearch,
} from "react-icons/fa";

// Backend API URL
const YOUTUBE_API_URL = "http://127.0.0.1:8000/process_youtube_video/";

// Mock data for comments
const COMMENTS = [
  {
    id: 1,
    user: {
      name: "<PERSON>",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
    },
    text: "This video was incredibly helpful! I've been struggling with this concept for weeks and now it finally makes sense.",
    likes: 124,
    time: "2 weeks ago",
    replies: [
      {
        id: 101,
        user: {
          name: "<PERSON>",
          avatar: "https://randomuser.me/api/portraits/women/44.jpg",
        },
        text: "I agree! The explanation at 3:42 was particularly clear.",
        likes: 18,
        time: "1 week ago",
      },
    ],
  },
  {
    id: 2,
    user: {
      name: "<PERSON>",
      avatar: "https://randomuser.me/api/portraits/men/67.jpg",
    },
    text: "Great content as always. Would love to see a follow-up video on advanced techniques!",
    likes: 89,
    time: "3 days ago",
    replies: [],
  },
  {
    id: 3,
    user: {
      name: "<PERSON> <PERSON>",
      avatar: "https://randomuser.me/api/portraits/women/28.jpg",
    },
    text: "I've watched this three times now and learn something new each time. The examples are so practical.",
    likes: 56,
    time: "5 days ago",
    replies: [],
  },
  {
    id: 4,
    user: {
      name: "David Wilson",
      avatar: "https://randomuser.me/api/portraits/men/52.jpg",
    },
    text: "Question: Does this approach work for larger datasets as well? I'm working with millions of records.",
    likes: 12,
    time: "1 day ago",
    replies: [
      {
        id: 102,
        user: {
          name: "Channel Owner",
          avatar: "https://randomuser.me/api/portraits/men/1.jpg",
          isVerified: true,
        },
        text: "Yes, it scales well! Check out my other video on optimization techniques for large datasets.",
        likes: 34,
        time: "1 day ago",
      },
    ],
  },
];

// Mock video metadata generator
const generateVideoMetadata = (video) => {
  const viewCount = Math.floor(Math.random() * 900000) + 100000;
  const formattedViews =
    viewCount > 1000000
      ? `${(viewCount / 1000000).toFixed(1)}M`
      : `${Math.floor(viewCount / 1000)}K`;

  const likes = Math.floor(viewCount * (Math.random() * 0.05 + 0.02));
  const formattedLikes =
    likes > 1000 ? `${(likes / 1000).toFixed(1)}K` : likes.toString();

  const uploadDate = new Date();
  uploadDate.setDate(uploadDate.getDate() - Math.floor(Math.random() * 365));

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const formattedDate = `${
    months[uploadDate.getMonth()]
  } ${uploadDate.getDate()}, ${uploadDate.getFullYear()}`;

  return {
    views: formattedViews,
    likes: formattedLikes,
    uploadDate: formattedDate,
    channelName: "EduTech Academy",
    channelAvatar: "https://randomuser.me/api/portraits/men/1.jpg",
    subscribers: "1.2M",
    isSubscribed: Math.random() > 0.5,
    description: `In this comprehensive tutorial, we explore ${
      video?.title || "advanced concepts"
    } in detail.

This video covers:
• Core principles and fundamentals
• Practical examples and use cases
• Common pitfalls and how to avoid them
• Advanced techniques for experienced users

📚 Resources mentioned in this video:
- Documentation: https://docs.example.com
- GitHub repository: https://github.com/example/repo
- Practice exercises: https://exercises.example.com

🔔 Subscribe for weekly tutorials and updates!

#Tutorial #Learning #Education`,
  };
};

const VideoPlayer = ({ selectedVideo }) => {
  const [videoMetadata, setVideoMetadata] = useState(null);
  const [comments, setComments] = useState([]);
  const [showAllDescription, setShowAllDescription] = useState(false);
  const [query, setQuery] = useState("");
  const [apiResponse, setApiResponse] = useState(null);
  const [manualTimestamp, setManualTimestamp] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [answers, setAnswers] = useState([]);

  console.log(answers);

  useEffect(() => {
    if (selectedVideo) {
      // Generate mock metadata for the selected video
      setVideoMetadata(generateVideoMetadata(selectedVideo));
      // Set mock comments
      setComments(COMMENTS);
      setAnswers([]); // Reset answers on video change
    }
  }, [selectedVideo]);

  // API call for query using YouTube URL
  const handleQuerySubmit = async (e) => {
    e.preventDefault();

    if (!query.trim()) {
      alert("Please enter a query");
      return;
    }

    if (!selectedVideo || !selectedVideo.youtubeId) {
      alert("No video selected");
      return;
    }

    setIsSearching(true);
    setUploadProgress(0);

    try {
      const youtubeUrl = `https://www.youtube.com/watch?v=${selectedVideo.youtubeId}`;

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          const newProgress = prev + 5;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 1000);

      // Make API request to the YouTube endpoint
      const response = await fetch(YOUTUBE_API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          youtube_url: youtubeUrl,
          query: query,
        }),
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      setApiResponse(result);

      // Handle answer as an array
      const answerArr = Array.isArray(result.answer)
        ? result.answer
        : result.answer
        ? [result.answer]
        : [];
      setAnswers(answerArr);

      // Update video metadata if available
      if (result.video_info) {
        const updatedMetadata = {
          ...videoMetadata,
          views: `${Math.floor(Math.random() * 500) + 100}K`,
          likes: `${Math.floor(Math.random() * 50) + 10}K`,
          uploadDate: new Date().toLocaleDateString(),
          channelName: result.video_info.author,
          subscribers: "1.2M",
          description: `${result.video_info.title}\n\nThis video is ${
            result.video_info.length_seconds
          } seconds long.\n\n${videoMetadata?.description || ""}`,
        };
        setVideoMetadata(updatedMetadata);
      }

      setIsSearching(false);
    } catch (error) {
      console.error("Error submitting query:", error);
      alert(`Error: ${error.message}`);
      setIsSearching(false);
    }
  };

  // Accept timestamp as argument
  const handleJumpToTimestamp = (timestamp) => {
    const time = Number.parseFloat(timestamp);
    if (isNaN(time) || time < 0) {
      alert("Please enter a valid timestamp (non-negative number).");
      return;
    }
    const iframe = document.querySelector(".youtube-player iframe");
    if (iframe) {
      iframe.contentWindow.postMessage(
        JSON.stringify({
          event: "command",
          func: "seekTo",
          args: [time, true],
        }),
        "*"
      );
    }
  };

  const formatDescription = (text) => {
    if (!text) return "";

    // Split by newlines and process each line
    const lines = text.split("\n");

    return lines.map((line, index) => {
      // Convert URLs to links
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const processedLine = line.replace(
        urlRegex,
        '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
      );

      // Add line breaks
      return (
        <p key={index} dangerouslySetInnerHTML={{ __html: processedLine }} />
      );
    });
  };

  if (!selectedVideo) {
    return (
      <div className="video-player-container">
        <div className="video-placeholder">
          <h2>Select a video from the sidebar to start watching</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="video-player-container">
      {/* Video Player */}
      <div className="youtube-player">
        <iframe
          width="100%"
          height="100%"
          src={`https://www.youtube.com/embed/${selectedVideo.youtubeId}?enablejsapi=1`}
          title={selectedVideo.title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        ></iframe>
      </div>

      {/* Video Title and Actions */}
      <div className="video-title-section">
        <h1>{selectedVideo.title}</h1>
      </div>

      {/* Video Info Banner */}
      <div className="video-info-banner">
        <p>Enter your query below to find specific moments in this video</p>
      </div>

      {/* Query Search Box */}
      <div className="query-search-box">
        <form onSubmit={handleQuerySubmit} style={{ width: "100%" }}>
          <div className="query-input-group" style={{ width: "100%" }}>
            <QueryInput
              query={query}
              setQuery={setQuery}
              onSubmit={handleQuerySubmit}
              isSearching={isSearching}
            />
          </div>

          {/* FIXED: Use the stable progress bar component */}
          <ProgressBarSection
            isSearching={isSearching}
            uploadProgress={uploadProgress}
          />

          {/* FIXED: Stable API response container */}
          {apiResponse && (
            <div
              className="query-response"
              style={{
                width: "100%",
                minHeight: apiResponse ? "auto" : "0",
                transition: "min-height 0.3s ease",
              }}
            >
              <div
                className="answers-list"
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "1.5em",
                  marginTop: "1em",
                  width: "100%",
                  boxSizing: "border-box",
                  opacity: apiResponse ? 1 : 0,
                  transform: apiResponse
                    ? "translateY(0)"
                    : "translateY(-10px)",
                  transition: "opacity 0.3s ease, transform 0.3s ease",
                }}
              >
                {answers.map((ans, idx) => {
                  const jumpTime = ans.timestampstart;
                  return (
                    <div
                      key={idx}
                      className="answer-block"
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "flex-start",
                        justifyContent: "space-between",
                        background: "#f8f9fa",
                        borderRadius: "8px",
                        boxShadow: "0 1px 4px rgba(0,0,0,0.06)",
                        padding: "1em",
                        minHeight: "80px", // FIXED: Consistent height
                        gap: "1em",
                        width: "100%",
                        boxSizing: "border-box",
                        flex: "1 1 100%",
                      }}
                    >
                      <div
                        style={{ flex: 1, flexDirection: "row", minWidth: 0 }}
                      >
                        <div
                          style={{
                            fontWeight: 600,
                            fontSize: "1.05em",
                            marginBottom: 4,
                          }}
                        >
                          Answer {answers.length > 1 ? idx + 1 : ""}:
                        </div>
                        <div style={{ color: "#222", marginBottom: 6 }}>
                          {ans.answer ||
                            ans.text ||
                            `Found at ${jumpTime} seconds`}
                        </div>
                        {ans.text && (
                          <div
                            style={{
                              fontStyle: "italic",
                              color: "#4b5563",
                              background: "#eef2f7",
                              borderRadius: 4,
                              padding: "0.5em 0.75em",
                              marginTop: 2,
                              fontSize: "0.98em",
                              wordBreak: "break-word",
                            }}
                          >
                            {ans.text}
                          </div>
                        )}
                        {ans.timestampend !== undefined &&
                          !isNaN(ans.timestampend) && (
                            <span
                              style={{
                                marginLeft: 0,
                                color: "#888",
                                fontSize: "0.95em",
                                display: "block",
                                marginTop: 4,
                              }}
                            >
                              [{jumpTime?.toFixed(2)}s -{" "}
                              {ans.timestampend.toFixed(2)}s]
                            </span>
                          )}
                      </div>
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "flex-end",
                          justifyContent: "center",
                        }}
                      >
                        {jumpTime !== undefined && !isNaN(jumpTime) && (
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleJumpToTimestamp(jumpTime);
                            }}
                            className="jump-button"
                            style={{
                              background: "#2563eb",
                              color: "white",
                              border: "none",
                              borderRadius: 6,
                              padding: "0.6em 1.2em",
                              fontWeight: 600,
                              fontSize: "1em",
                              cursor: "pointer",
                              minWidth: "140px", // FIXED: Consistent button width
                              height: "36px", // FIXED: Consistent height
                              boxShadow: "0 1px 2px rgba(0,0,0,0.04)",
                              transition: "background 0.2s",
                            }}
                          >
                            Jump to {jumpTime.toFixed(1)}s
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </form>
      </div>

      {/* Video Stats and Actions */}
      <div className="video-stats-section">
        <div className="video-stats">
          <span className="views">{videoMetadata?.views || "0"} views</span>
          <span className="upload-date">
            {videoMetadata?.uploadDate || "Unknown date"}
          </span>
        </div>

        <div className="video-actions">
          <button className="action-button">
            <FaThumbsUp />
            <span>{videoMetadata?.likes || "0"}</span>
          </button>
          <button className="action-button">
            <FaThumbsDown />
            <span>Dislike</span>
          </button>
          <button className="action-button">
            <FaShare />
            <span>Share</span>
          </button>
          <button className="action-button">
            <FaDownload />
            <span>Download</span>
          </button>
          <button className="action-button">
            <FaEllipsisH />
          </button>
        </div>
      </div>

      {/* Channel Info */}
      <div className="channel-section">
        <div className="channel-info">
          <img
            src={videoMetadata?.channelAvatar}
            alt={videoMetadata?.channelName}
            className="channel-avatar"
          />
          <div className="channel-details">
            <h3>{videoMetadata?.channelName || "Channel Name"}</h3>
            <p>{videoMetadata?.subscribers || "0"} subscribers</p>
          </div>
        </div>

        <button
          className={`subscribe-button ${
            videoMetadata?.isSubscribed ? "subscribed" : ""
          }`}
        >
          {videoMetadata?.isSubscribed ? (
            <>
              <FaRegBell />
              <span>Subscribed</span>
            </>
          ) : (
            <span>Subscribe</span>
          )}
        </button>
      </div>

      {/* Description */}
      <div className="video-description">
        <div
          className={`description-text ${showAllDescription ? "expanded" : ""}`}
        >
          {formatDescription(videoMetadata?.description)}
        </div>
        <button
          className="show-more-button"
          onClick={() => setShowAllDescription(!showAllDescription)}
        >
          {showAllDescription ? "Show less" : "Show more"}
        </button>
      </div>

      {/* Comments Section */}
      <div className="comments-section">
        <div className="comments-header">
          <h3>{comments.length} Comments</h3>
          <div className="comments-sort">
            <FaSearch />
            <span>Sort by</span>
          </div>
        </div>

        {/* Comment Input */}
        <div className="comment-input-container">
          <img
            src="https://randomuser.me/api/portraits/men/85.jpg"
            alt="Your Avatar"
            className="user-avatar"
          />
          <input
            type="text"
            placeholder="Add a comment..."
            className="comment-input"
          />
        </div>

        {/* Comments List */}
        <div className="comments-list">
          {comments.map((comment) => (
            <div key={comment.id} className="comment">
              <img
                src={comment.user.avatar}
                alt={comment.user.name}
                className="comment-avatar"
              />
              <div className="comment-content">
                <div className="comment-header">
                  <span className="comment-author">{comment.user.name}</span>
                  <span className="comment-time">{comment.time}</span>
                </div>
                <p className="comment-text">{comment.text}</p>
                <div className="comment-actions">
                  <button className="comment-action">
                    <FaThumbsUp />
                    <span>{comment.likes}</span>
                  </button>
                  <button className="comment-action">
                    <FaThumbsDown />
                  </button>
                  <button className="comment-action">Reply</button>
                </div>

                {/* Replies */}
                {comment.replies.length > 0 && (
                  <div className="comment-replies">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="comment reply">
                        <img
                          src={reply.user.avatar}
                          alt={reply.user.name}
                          className="comment-avatar"
                        />
                        <div className="comment-content">
                          <div className="comment-header">
                            <span className="comment-author">
                              {reply.user.name}
                              {reply.user.isVerified && (
                                <span className="verified-badge">✓</span>
                              )}
                            </span>
                            <span className="comment-time">{reply.time}</span>
                          </div>
                          <p className="comment-text">{reply.text}</p>
                          <div className="comment-actions">
                            <button className="comment-action">
                              <FaThumbsUp />
                              <span>{reply.likes}</span>
                            </button>
                            <button className="comment-action">
                              <FaThumbsDown />
                            </button>
                            <button className="comment-action">Reply</button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Animated QueryInput component
const sampleQueries = [
  "summarize this video",
  "find out timestamp for - ...",
  "when does speaker talk about ...",
];

function QueryInput({ query, setQuery, onSubmit, isSearching }) {
  const [placeholder, setPlaceholder] = useState("");
  const [currentIdx, setCurrentIdx] = useState(0);
  const [typing, setTyping] = useState(true);
  const [flying, setFlying] = useState(false);
  const [flyText, setFlyText] = useState("");
  const [isInputFocused, setIsInputFocused] = useState(false); // New state to track input focus
  const typingTimeout = useRef();
  const flyTimeout = useRef();

  // Typing animation
  useEffect(() => {
    let idx = 0;
    let active = true;
    // Stop animation if input is focused
    if (isInputFocused) {
      clearTimeout(typingTimeout.current);
      return;
    }

    function typeOut() {
      if (!active || isInputFocused) return; // Add check here too
      const text = sampleQueries[currentIdx];
      if (idx <= text.length) {
        setPlaceholder(text.slice(0, idx));
        idx++;
        typingTimeout.current = setTimeout(typeOut, 60);
      } else {
        setTimeout(() => {
          setFlying(true);
          setFlyText(text);
          setTyping(false);
        }, 1200);
      }
    }
    if (typing && !isInputFocused) typeOut(); // Only start if input is not focused
    return () => {
      active = false;
      clearTimeout(typingTimeout.current);
    };
    // Add isInputFocused to dependencies
  }, [currentIdx, typing, isInputFocused]);

  // Flying animation and loop
  useEffect(() => {
    // Stop animation if input is focused
    if (isInputFocused) {
      clearTimeout(flyTimeout.current);
      return;
    }

    if (flying && !isInputFocused) {
      // Only start if input is not focused
      flyTimeout.current = setTimeout(() => {
        setFlying(false);
        setTyping(true);
        setPlaceholder("");
        setCurrentIdx((prev) => (prev + 1) % sampleQueries.length);
      }, 900);
    }
    return () => clearTimeout(flyTimeout.current);
    // Add isInputFocused to dependencies
  }, [flying, isInputFocused]);

  // Effect to clear timers when input is focused and restart when blurred if query is empty
  useEffect(() => {
    if (isInputFocused) {
      clearTimeout(typingTimeout.current);
      clearTimeout(flyTimeout.current);
      setTyping(false); // Stop typing animation
      setFlying(false); // Stop flying animation
      setPlaceholder(""); // Clear placeholder text
    } else {
      // When input is blurred, restart the typing animation if query is empty
      if (!query) {
        setTyping(true);
      }
    }
  }, [isInputFocused, query]); // Add query to dependencies here

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        height: "80px", // Set a fixed height for the container
      }}
    >
      {/* Input and button area */}
      <div
        style={{
          position: "absolute", // Position below flying text
          left: 0,
          top: "20px", // Position 20px from the top (below flying text)
          width: "100%",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          gap: 8,
          height: "40px", // Fixed height for input area
        }}
      >
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsInputFocused(true)} // Set focused to true on focus
          onBlur={() => setIsInputFocused(false)} // Set focused to false on blur
          placeholder={isInputFocused ? "" : placeholder} // Only show placeholder animation if not focused
          className="query-input"
          style={{
            width: "100%",
            flex: 1,
            minWidth: "300px",
            // Further reduced vertical padding
            padding: "10px 12px", // Reduced vertical padding from 4px to 2px
          }}
          autoComplete="off"
        />
        <button
          type="submit"
          className="query-button"
          disabled={isSearching}
          style={{
            marginTop: 0,
            marginLeft: 8,
            whiteSpace: "nowrap",
            height: "40px", // Ensure button takes the height of its container
            minWidth: "120px",
          }}
        >
          {isSearching ? "Searching..." : "Find Moment"}
        </button>
      </div>
    </div>
  );
}

// Fixed progress bar section
const ProgressBarSection = ({ isSearching, uploadProgress }) => {
  return (
    <div
      style={{
        minHeight: isSearching ? "auto" : "0", // FIXED: Smooth height transition
        overflow: "hidden",
        transition: "min-height 0.3s ease",
      }}
    >
      {isSearching && (
        <div
          className="upload-progress"
          style={{
            opacity: isSearching ? 1 : 0,
            transform: isSearching ? "translateY(0)" : "translateY(-10px)",
            transition: "opacity 0.3s ease, transform 0.3s ease",
          }}
        >
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{
                width: `${uploadProgress}%`,
                transition: "width 0.3s ease", // Smooth progress animation
              }}
            ></div>
          </div>
          <span>
            {uploadProgress}% - Processing video and finding answer...
          </span>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;
