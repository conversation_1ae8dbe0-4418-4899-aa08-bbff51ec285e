/* Global Styles */
:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --secondary-color: #10b981;
  --secondary-hover: #059669;
  --accent-color: #f59e0b;
  --danger-color: #ef4444;
  --light-bg: #f1f5f9;
  --dark-bg: #1e293b;
  --card-bg: #ffffff;
  --border-color: #cbd5e1;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-light: #f8fafc;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  color: var(--text-primary);
  background: var(--light-bg);
  margin: 0;
  padding: 0;
}

/* App Wrapper */
.app-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Topics Page Styles */
.topics-container {
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.topics-header {
  text-align: center;
  margin-bottom: 3rem;
}

.topics-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--dark-bg);
  margin-bottom: 0.5rem;
}

.topics-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
}

.topics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  width: 100%;
  max-width: 1200px;
}

.topic-card {
  background: var(--card-bg);
  border-radius: 1rem;
  border-left: 5px solid;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.topic-card:hover,
.topic-card.hovered {
  transform: translateY(-5px);
}

.topic-icon {
  margin-right: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.topic-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.topic-content p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Lectures Page Styles */
.lectures-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--light-bg);
}

.back-button {
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
}

.back-button svg {
  margin-right: 0.5rem;
  color: var(--primary-color);
}

.lectures-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 60px);
}

.sidebar-wrapper {
  width: 300px;
  background: var(--card-bg);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
}

.video-player-wrapper {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

/* Sidebar Styles */
.sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header h2 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.sidebar-header p {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.sidebar-content {
  flex: 1;
  padding: 1rem 0;
}

.topic-accordion {
  margin-bottom: 0.5rem;
}

.topic-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.topic-header:hover {
  background-color: rgba(79, 70, 229, 0.1);
}

.accordion-icon {
  margin-right: 0.75rem;
  font-size: 0.75rem;
  color: var(--primary-color);
}

.video-list {
  padding-left: 2.5rem;
}

.video-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  margin: 0.25rem 0;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.video-item:hover {
  background-color: rgba(79, 70, 229, 0.1);
}

.video-icon {
  margin-right: 0.5rem;
  font-size: 0.75rem;
  color: var(--secondary-color);
}

/* Video Player Styles */
.video-player-container {
  background: var(--card-bg);
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding-bottom: 2rem;
}

.video-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #f1f1f1;
  color: var(--text-secondary);
  text-align: center;
  padding: 2rem;
}

.youtube-player {
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
  position: relative;
  background: #000;
}

.youtube-player iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-title-section {
  padding: 1rem 1.5rem 0.5rem;
}

.video-title-section h1 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.4;
}

/* Video Info Banner */
.video-info-banner {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background-color: #f0f4f8;
  text-align: center;
}

.video-info-banner p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Query Search Box Styles */
.query-search-box {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background-color: #f8f9fa;
}

.query-input-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.query-input {
  flex-grow: 1;
  padding: 0.25rem 1rem; /* Reduced vertical padding */
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.query-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 5px rgba(79, 70, 229, 0.5);
}

.query-button {
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
  font-weight: 500;
}

.query-button:hover:not(:disabled) {
  background: var(--primary-hover);
}

.query-button:disabled {
  background: #a5a5a5;
  cursor: not-allowed;
}

.upload-progress {
  margin: 1rem 0;
  background: #f1f5f9;
  padding: 1rem;
  border-radius: 0.5rem;
}

.progress-bar {
  height: 0.5rem;
  background: #e2e8f0;
  border-radius: 9999px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 9999px;
  transition: width 0.3s ease;
}

.query-response {
  background: #eef2ff;
  border: 1px solid #c7d2fe;
  padding: 1rem;
  border-radius: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.query-response p {
  margin: 0;
  font-size: 0.875rem;
}

.video-stats-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1.5rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.video-stats {
  display: flex;
  gap: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.video-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--light-bg);
  border: none;
  border-radius: 9999px;
  font-size: 0.875rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: background 0.2s;
}

.action-button:hover {
  background: #e2e8f0;
}

.channel-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.channel-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.channel-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.channel-details h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
}

.channel-details p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.subscribe-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #0f0f0f;
  color: white;
  border: none;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.subscribe-button:hover {
  background: #272727;
}

.subscribe-button.subscribed {
  background: var(--light-bg);
  color: var(--text-primary);
}

.subscribe-button.subscribed:hover {
  background: #e2e8f0;
}

.video-description {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.description-text {
  font-size: 0.875rem;
  line-height: 1.5;
  max-height: 100px;
  overflow: hidden;
  position: relative;
}

.description-text.expanded {
  max-height: none;
}

.description-text p {
  margin: 0.5rem 0;
}

.description-text a {
  color: var(--primary-color);
  text-decoration: none;
}

.description-text a:hover {
  text-decoration: underline;
}

.show-more-button {
  margin-top: 0.5rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0;
}

.timestamp-search {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.timestamp-search h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem;
}

.input-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.input-field {
  flex-grow: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 5px rgba(79, 70, 229, 0.5);
}

.jump-button {
  padding: 0.5rem 1rem;
  background: var(--secondary-color);
  color: white;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
  font-weight: 500;
}

.jump-button:hover {
  background: var(--secondary-hover);
}

.comments-section {
  padding: 1rem 1.5rem;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.comments-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.comments-sort {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
}

.comment-input-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.user-avatar,
.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.comment-input {
  flex-grow: 1;
  border: none;
  border-bottom: 1px solid var(--border-color);
  padding: 0.5rem 0;
  font-size: 0.875rem;
}

.comment-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.comment {
  display: flex;
  gap: 1rem;
}

.comment-content {
  flex-grow: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.comment-author {
  font-size: 0.875rem;
  font-weight: 500;
}

.verified-badge {
  color: var(--primary-color);
  margin-left: 0.25rem;
}

.comment-time {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.comment-text {
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 0.5rem;
}

.comment-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.comment-action {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  font-size: 0.75rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
}

.comment-replies {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.comment.reply {
  margin-left: 1rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .topics-grid {
    grid-template-columns: 1fr;
  }

  .lectures-content {
    flex-direction: column;
    height: auto;
  }

  .sidebar-wrapper {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .video-player-wrapper {
    padding: 1rem;
  }
}
